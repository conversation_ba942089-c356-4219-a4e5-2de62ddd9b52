# ColPali GPU Memory Troubleshooting Guide

## Problem Description

The ColPali embedding model requires significant GPU memory (~4GB) to load and run. When GPU memory is insufficient, you may encounter errors like:

```
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 20.00 MiB. GPU 0 has a total capacity of 14.56 GiB of which 10.75 MiB is free.
```

## Solutions

### 1. Force CPU Mode (Recommended for Limited GPU Memory)

Set `colpali_force_cpu = true` in your configuration file:

**For local development (`morphik.toml`):**
```toml
[morphik]
colpali_force_cpu = true
```

**For Docker deployment (`morphik.docker.toml`):**
```toml
[morphik]
colpali_force_cpu = true  # Already set to true by default
```

### 2. Use ColPali API Mode

Instead of running the model locally, use the API mode:

```toml
[morphik]
colpali_mode = "api"
morphik_embedding_api_domain = "http://your-colpali-api-endpoint"
```

You'll need to set the `MORPHIK_EMBEDDING_API_KEY` environment variable.

### 3. Disable ColPali Entirely

If you don't need multimodal (image + text) embeddings:

```toml
[morphik]
enable_colpali = false
```

### 4. GPU Memory Optimization

If you want to keep using GPU mode, try these optimizations:

1. **Clear GPU cache before starting:**
   ```bash
   python -c "import torch; torch.cuda.empty_cache()"
   ```

2. **Reduce batch size** (automatically handled in the updated code)

3. **Use mixed precision** (already implemented with bfloat16)

## Testing Your Configuration

Use the provided test script to check if your configuration works:

```bash
python test_colpali_memory.py
```

This script will:
- Check GPU memory availability
- Test ColPali model initialization
- Generate a test embedding
- Report memory usage

## Configuration Options

| Option | Values | Description |
|--------|--------|-------------|
| `enable_colpali` | `true`/`false` | Enable/disable ColPali entirely |
| `colpali_mode` | `"off"`, `"local"`, `"api"` | How to run ColPali |
| `colpali_force_cpu` | `true`/`false` | Force CPU even if GPU available |

## Performance Considerations

### GPU vs CPU Performance

- **GPU**: Fast inference (~0.1-0.5s per embedding)
- **CPU**: Slower inference (~2-10s per embedding)

### Memory Requirements

- **GPU**: ~4GB VRAM for the model
- **CPU**: ~2GB RAM for the model

## Automatic Fallback

The updated ColPali implementation includes automatic fallback:

1. **Memory Check**: Checks available GPU memory before loading
2. **Automatic CPU Fallback**: Falls back to CPU if GPU memory is insufficient
3. **Error Recovery**: Catches CUDA OOM errors and retries with CPU

## Environment Variables

You can also control the behavior via environment variables:

```bash
# Force CPU mode
export COLPALI_FORCE_CPU=true

# Set API key for API mode
export MORPHIK_EMBEDDING_API_KEY=your-api-key
```

## Troubleshooting Steps

1. **Check GPU memory:**
   ```bash
   nvidia-smi
   ```

2. **Test configuration:**
   ```bash
   python test_colpali_memory.py
   ```

3. **Check logs** for memory-related warnings and errors

4. **Try CPU mode** if GPU issues persist

5. **Consider API mode** for production deployments

## Docker Considerations

For Docker deployments:
- GPU passthrough requires proper NVIDIA Docker setup
- CPU mode is often more reliable in containerized environments
- The `morphik.docker.toml` defaults to `colpali_force_cpu = true`

## Support

If you continue to experience issues:
1. Run the test script and share the output
2. Include your GPU specifications and available memory
3. Share your configuration file (with sensitive data removed)
