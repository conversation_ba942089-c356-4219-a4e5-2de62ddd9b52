#!/bin/bash

# Quick fix script for ColPali GPU memory issues
# This script provides multiple solutions to resolve CUDA out of memory errors

echo "=== ColPali Memory Fix Script ==="
echo ""

# Function to check if running in Docker
check_docker() {
    if [ -f /.dockerenv ]; then
        echo "✓ Running in Docker container"
        return 0
    else
        echo "✗ Not running in Docker"
        return 1
    fi
}

# Function to set environment variable for CPU mode
set_cpu_mode() {
    echo "Setting COLPALI_FORCE_CPU environment variable..."
    export COLPALI_FORCE_CPU=true
    echo "export COLPALI_FORCE_CPU=true" >> ~/.bashrc
    echo "✓ Environment variable set"
}

# Function to update Docker configuration
update_docker_config() {
    if [ -f "morphik.docker.toml" ]; then
        echo "Updating morphik.docker.toml..."
        
        # Check if colpali_force_cpu already exists
        if grep -q "colpali_force_cpu" morphik.docker.toml; then
            # Update existing line
            sed -i 's/colpali_force_cpu = false/colpali_force_cpu = true/g' morphik.docker.toml
            sed -i 's/colpali_force_cpu=false/colpali_force_cpu = true/g' morphik.docker.toml
            echo "✓ Updated existing colpali_force_cpu setting"
        else
            # Add new line after colpali_mode
            sed -i '/colpali_mode = "local"/a colpali_force_cpu = true  # Force CPU to avoid GPU memory issues' morphik.docker.toml
            echo "✓ Added colpali_force_cpu setting"
        fi
    else
        echo "⚠ morphik.docker.toml not found"
    fi
}

# Function to update local configuration
update_local_config() {
    if [ -f "morphik.toml" ]; then
        echo "Updating morphik.toml..."
        
        # Check if colpali_force_cpu already exists
        if grep -q "colpali_force_cpu" morphik.toml; then
            # Update existing line
            sed -i 's/colpali_force_cpu = false/colpali_force_cpu = true/g' morphik.toml
            sed -i 's/colpali_force_cpu=false/colpali_force_cpu = true/g' morphik.toml
            echo "✓ Updated existing colpali_force_cpu setting"
        else
            # Add new line after colpali_mode
            sed -i '/colpali_mode = "local"/a colpali_force_cpu = true  # Force CPU to avoid GPU memory issues' morphik.toml
            echo "✓ Added colpali_force_cpu setting"
        fi
    else
        echo "⚠ morphik.toml not found"
    fi
}

# Function to disable ColPali entirely
disable_colpali() {
    echo "Disabling ColPali entirely..."
    
    if [ -f "morphik.docker.toml" ]; then
        sed -i 's/enable_colpali = true/enable_colpali = false/g' morphik.docker.toml
        echo "✓ Disabled ColPali in morphik.docker.toml"
    fi
    
    if [ -f "morphik.toml" ]; then
        sed -i 's/enable_colpali = true/enable_colpali = false/g' morphik.toml
        echo "✓ Disabled ColPali in morphik.toml"
    fi
}

# Function to check GPU memory
check_gpu_memory() {
    echo "Checking GPU memory..."
    if command -v nvidia-smi &> /dev/null; then
        nvidia-smi --query-gpu=memory.total,memory.used,memory.free --format=csv,noheader,nounits
    else
        echo "⚠ nvidia-smi not available"
    fi
}

# Main menu
echo "Choose a solution:"
echo "1. Force CPU mode (recommended)"
echo "2. Disable ColPali entirely"
echo "3. Check GPU memory"
echo "4. Apply all fixes"
echo "5. Exit"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo ""
        echo "=== Forcing CPU Mode ==="
        set_cpu_mode
        update_docker_config
        update_local_config
        echo ""
        echo "✓ CPU mode enabled. Restart the application to apply changes."
        ;;
    2)
        echo ""
        echo "=== Disabling ColPali ==="
        disable_colpali
        echo ""
        echo "✓ ColPali disabled. Restart the application to apply changes."
        ;;
    3)
        echo ""
        echo "=== GPU Memory Status ==="
        check_gpu_memory
        ;;
    4)
        echo ""
        echo "=== Applying All Fixes ==="
        set_cpu_mode
        update_docker_config
        update_local_config
        check_gpu_memory
        echo ""
        echo "✓ All fixes applied. Restart the application to apply changes."
        ;;
    5)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "Invalid choice. Exiting..."
        exit 1
        ;;
esac

echo ""
echo "=== Next Steps ==="
echo "1. Restart your application/container"
echo "2. Check the logs for 'ColPali forced to use CPU'"
echo "3. If issues persist, consider disabling ColPali entirely"
echo ""
echo "For more help, see: COLPALI_MEMORY_TROUBLESHOOTING.md"
