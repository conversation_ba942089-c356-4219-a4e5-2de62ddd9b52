#!/usr/bin/env python3
"""
Quick fix script for ColPali GPU memory issues.
This script modifies the configuration to force CPU mode.
"""

import os
import sys
from pathlib import Path


def update_toml_file(file_path, force_cpu=True):
    """Update TOML file to force CPU mode or disable ColPali."""
    if not os.path.exists(file_path):
        print(f"⚠ {file_path} not found")
        return False
    
    print(f"Updating {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check if colpali_force_cpu already exists
    if 'colpali_force_cpu' in content:
        # Update existing setting
        content = content.replace('colpali_force_cpu = false', 'colpali_force_cpu = true')
        content = content.replace('colpali_force_cpu=false', 'colpali_force_cpu = true')
        print(f"✓ Updated existing colpali_force_cpu setting in {file_path}")
    else:
        # Add new setting after colpali_mode
        if 'colpali_mode = "local"' in content:
            content = content.replace(
                'colpali_mode = "local"',
                'colpali_mode = "local"\ncolpali_force_cpu = true  # Force CPU to avoid GPU memory issues'
            )
            print(f"✓ Added colpali_force_cpu setting to {file_path}")
        else:
            print(f"⚠ Could not find colpali_mode setting in {file_path}")
            return False
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)
    
    return True


def disable_colpali(file_path):
    """Disable ColPali entirely."""
    if not os.path.exists(file_path):
        print(f"⚠ {file_path} not found")
        return False
    
    print(f"Disabling ColPali in {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    content = content.replace('enable_colpali = true', 'enable_colpali = false')
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✓ Disabled ColPali in {file_path}")
    return True


def main():
    """Main function."""
    print("=== ColPali Quick Fix ===")
    print("")
    
    # Get the current directory
    current_dir = Path.cwd()
    
    # Check for configuration files
    docker_config = current_dir / "morphik.docker.toml"
    local_config = current_dir / "morphik.toml"
    
    print("Choose a fix:")
    print("1. Force CPU mode (recommended)")
    print("2. Disable ColPali entirely")
    print("3. Exit")
    print("")
    
    try:
        choice = input("Enter your choice (1-3): ").strip()
    except KeyboardInterrupt:
        print("\nExiting...")
        return 0
    
    if choice == "1":
        print("")
        print("=== Forcing CPU Mode ===")
        
        success = False
        if docker_config.exists():
            success |= update_toml_file(str(docker_config))
        
        if local_config.exists():
            success |= update_toml_file(str(local_config))
        
        if success:
            print("")
            print("✓ CPU mode enabled successfully!")
            print("Restart your application to apply the changes.")
        else:
            print("")
            print("❌ Failed to update configuration files.")
            return 1
            
    elif choice == "2":
        print("")
        print("=== Disabling ColPali ===")
        
        success = False
        if docker_config.exists():
            success |= disable_colpali(str(docker_config))
        
        if local_config.exists():
            success |= disable_colpali(str(local_config))
        
        if success:
            print("")
            print("✓ ColPali disabled successfully!")
            print("Restart your application to apply the changes.")
        else:
            print("")
            print("❌ Failed to update configuration files.")
            return 1
            
    elif choice == "3":
        print("Exiting...")
        return 0
    else:
        print("Invalid choice.")
        return 1
    
    print("")
    print("=== Next Steps ===")
    print("1. Restart your application/container")
    print("2. Check the logs for 'ColPali forced to use CPU' or 'ColPali disabled'")
    print("3. If issues persist, run this script again and try option 2")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
