#!/usr/bin/env python3
"""
Test script to check ColPali model memory usage and device compatibility.
This script helps diagnose GPU memory issues and test CPU fallback.
"""

import logging
import os
import sys
import time
from pathlib import Path

import torch

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config import get_settings
from core.embedding.colpali_embedding_model import ColpaliEmbeddingModel

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_gpu_memory():
    """Check GPU memory availability."""
    if not torch.cuda.is_available():
        logger.info("CUDA is not available")
        return None
    
    try:
        device_count = torch.cuda.device_count()
        logger.info(f"Found {device_count} CUDA device(s)")
        
        for i in range(device_count):
            props = torch.cuda.get_device_properties(i)
            total_memory = props.total_memory / (1024**3)  # GB
            allocated = torch.cuda.memory_allocated(i) / (1024**3)  # GB
            reserved = torch.cuda.memory_reserved(i) / (1024**3)  # GB
            free = total_memory - reserved
            
            logger.info(f"GPU {i} ({props.name}):")
            logger.info(f"  Total memory: {total_memory:.2f} GB")
            logger.info(f"  Allocated: {allocated:.2f} GB")
            logger.info(f"  Reserved: {reserved:.2f} GB")
            logger.info(f"  Free: {free:.2f} GB")
            
        return total_memory, free
    except Exception as e:
        logger.error(f"Error checking GPU memory: {e}")
        return None


def test_colpali_initialization():
    """Test ColPali model initialization."""
    logger.info("Testing ColPali model initialization...")
    
    try:
        start_time = time.time()
        model = ColpaliEmbeddingModel()
        init_time = time.time() - start_time
        
        logger.info(f"✅ ColPali model initialized successfully in {init_time:.2f}s")
        logger.info(f"Model device: {model.device}")
        logger.info(f"Model mode: {model.mode}")
        logger.info(f"Batch size: {model.batch_size}")
        
        return model
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize ColPali model: {e}")
        return None


def test_simple_embedding(model):
    """Test simple text embedding generation."""
    if model is None:
        return False
        
    logger.info("Testing simple text embedding...")
    
    try:
        import asyncio
        
        async def run_test():
            start_time = time.time()
            embedding = await model.embed_for_query("Hello, world!")
            embed_time = time.time() - start_time
            
            logger.info(f"✅ Text embedding generated successfully in {embed_time:.2f}s")
            logger.info(f"Embedding shape: {embedding.shape}")
            return True
            
        return asyncio.run(run_test())
        
    except Exception as e:
        logger.error(f"❌ Failed to generate text embedding: {e}")
        return False


def main():
    """Main test function."""
    logger.info("=== ColPali Memory Test ===")
    
    # Check current configuration
    try:
        settings = get_settings()
        logger.info(f"ENABLE_COLPALI: {settings.ENABLE_COLPALI}")
        logger.info(f"COLPALI_MODE: {settings.COLPALI_MODE}")
        logger.info(f"COLPALI_FORCE_CPU: {settings.COLPALI_FORCE_CPU}")
        logger.info(f"MODE: {settings.MODE}")
    except Exception as e:
        logger.error(f"Failed to load settings: {e}")
        return 1
    
    # Check GPU memory
    gpu_info = check_gpu_memory()
    
    # Test model initialization
    model = test_colpali_initialization()
    
    if model is None:
        logger.error("Model initialization failed. Check the logs above for details.")
        return 1
    
    # Test simple embedding
    if not test_simple_embedding(model):
        logger.error("Embedding generation failed. Check the logs above for details.")
        return 1
    
    # Check memory after initialization
    if torch.cuda.is_available() and model.device == "cuda":
        logger.info("=== GPU Memory After Model Loading ===")
        check_gpu_memory()
    
    logger.info("=== Test completed successfully! ===")
    return 0


if __name__ == "__main__":
    sys.exit(main())
